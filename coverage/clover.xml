<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1757685535799" clover="3.2.0">
  <project timestamp="1757685535799" name="All files">
    <metrics statements="212" coveredstatements="186" conditionals="80" coveredconditionals="71" methods="15" coveredmethods="10" elements="307" coveredelements="267" complexity="0" loc="212" ncloc="212" packages="1" files="3" classes="3"/>
    <file name="errors.ts" path="/Users/<USER>/WebstormProjects/filter-dsl/src/errors.ts">
      <metrics statements="11" coveredstatements="3" conditionals="3" coveredconditionals="0" methods="3" coveredmethods="0"/>
      <line num="19" count="1" type="stmt"/>
      <line num="26" count="0" type="stmt"/>
      <line num="27" count="0" type="stmt"/>
      <line num="37" count="1" type="stmt"/>
      <line num="44" count="0" type="stmt"/>
      <line num="45" count="0" type="stmt"/>
      <line num="49" count="1" type="stmt"/>
      <line num="50" count="0" type="cond" truecount="0" falsecount="3"/>
      <line num="52" count="0" type="stmt"/>
      <line num="54" count="0" type="stmt"/>
      <line num="56" count="0" type="stmt"/>
    </file>
    <file name="lexer.ts" path="/Users/<USER>/WebstormProjects/filter-dsl/src/lexer.ts">
      <metrics statements="179" coveredstatements="161" conditionals="75" coveredconditionals="69" methods="11" coveredmethods="9"/>
      <line num="1" count="1" type="stmt"/>
      <line num="2" count="1" type="stmt"/>
      <line num="4" count="1" type="cond" truecount="2" falsecount="0"/>
      <line num="5" count="1" type="stmt"/>
      <line num="6" count="1" type="stmt"/>
      <line num="7" count="1" type="stmt"/>
      <line num="8" count="1" type="stmt"/>
      <line num="9" count="1" type="stmt"/>
      <line num="10" count="1" type="stmt"/>
      <line num="11" count="1" type="stmt"/>
      <line num="12" count="1" type="stmt"/>
      <line num="13" count="1" type="stmt"/>
      <line num="14" count="1" type="stmt"/>
      <line num="17" count="1" type="stmt"/>
      <line num="23" count="93" type="stmt"/>
      <line num="24" count="93" type="stmt"/>
      <line num="25" count="93" type="stmt"/>
      <line num="26" count="93" type="cond" truecount="1" falsecount="1"/>
      <line num="27" count="93" type="stmt"/>
      <line num="31" count="1023" type="stmt"/>
      <line num="32" count="1023" type="cond" truecount="2" falsecount="0"/>
      <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="41" count="351" type="cond" truecount="2" falsecount="0"/>
      <line num="44" count="351" type="stmt"/>
      <line num="53" count="263" type="cond" truecount="2" falsecount="0"/>
      <line num="54" count="163" type="stmt"/>
      <line num="56" count="263" type="stmt"/>
      <line num="60" count="145" type="stmt"/>
      <line num="64" count="363" type="stmt"/>
      <line num="68" count="218" type="stmt"/>
      <line num="76" count="351" type="stmt"/>
      <line num="77" count="458" type="cond" truecount="10" falsecount="0"/>
      <line num="79" count="263" type="stmt"/>
      <line num="80" count="263" type="cond" truecount="1" falsecount="0"/>
      <line num="82" count="259" type="stmt"/>
      <line num="85" count="259" type="cond" truecount="8" falsecount="0"/>
      <line num="87" count="12" type="stmt"/>
      <line num="88" count="12" type="stmt"/>
      <line num="90" count="5" type="stmt"/>
      <line num="91" count="5" type="stmt"/>
      <line num="93" count="6" type="stmt"/>
      <line num="94" count="6" type="stmt"/>
      <line num="96" count="5" type="stmt"/>
      <line num="97" count="5" type="stmt"/>
      <line num="99" count="14" type="stmt"/>
      <line num="100" count="14" type="stmt"/>
      <line num="102" count="14" type="stmt"/>
      <line num="103" count="14" type="stmt"/>
      <line num="105" count="7" type="stmt"/>
      <line num="106" count="7" type="stmt"/>
      <line num="108" count="1" type="stmt"/>
      <line num="109" count="1" type="stmt"/>
      <line num="113" count="195" type="cond" truecount="3" falsecount="0"/>
      <line num="114" count="32" type="stmt"/>
      <line num="115" count="32" type="stmt"/>
      <line num="116" count="32" type="stmt"/>
      <line num="119" count="163" type="cond" truecount="1" falsecount="0"/>
      <line num="120" count="18" type="stmt"/>
      <line num="121" count="18" type="stmt"/>
      <line num="124" count="145" type="cond" truecount="1" falsecount="0"/>
      <line num="125" count="103" type="stmt"/>
      <line num="126" count="103" type="stmt"/>
      <line num="130" count="42" type="cond" truecount="1" falsecount="0"/>
      <line num="131" count="10" type="stmt"/>
      <line num="132" count="10" type="stmt"/>
      <line num="135" count="32" type="cond" truecount="1" falsecount="0"/>
      <line num="136" count="8" type="stmt"/>
      <line num="137" count="8" type="stmt"/>
      <line num="140" count="24" type="cond" truecount="1" falsecount="0"/>
      <line num="141" count="5" type="stmt"/>
      <line num="142" count="5" type="stmt"/>
      <line num="145" count="19" type="cond" truecount="1" falsecount="0"/>
      <line num="146" count="7" type="stmt"/>
      <line num="147" count="7" type="stmt"/>
      <line num="150" count="12" type="cond" truecount="1" falsecount="0"/>
      <line num="151" count="7" type="stmt"/>
      <line num="152" count="7" type="stmt"/>
      <line num="155" count="5" type="cond" truecount="1" falsecount="0"/>
      <line num="156" count="5" type="stmt"/>
      <line num="157" count="5" type="stmt"/>
      <line num="161" count="0" type="stmt"/>
      <line num="163" count="0" type="stmt"/>
      <line num="168" count="32" type="stmt"/>
      <line num="169" count="32" type="stmt"/>
      <line num="171" count="32" type="cond" truecount="2" falsecount="0"/>
      <line num="172" count="303" type="cond" truecount="1" falsecount="0"/>
      <line num="173" count="10" type="stmt"/>
      <line num="174" count="10" type="cond" truecount="1" falsecount="1"/>
      <line num="176" count="10" type="cond" truecount="6" falsecount="1"/>
      <line num="178" count="2" type="stmt"/>
      <line num="179" count="2" type="stmt"/>
      <line num="181" count="1" type="stmt"/>
      <line num="182" count="1" type="stmt"/>
      <line num="184" count="1" type="stmt"/>
      <line num="185" count="1" type="stmt"/>
      <line num="187" count="1" type="stmt"/>
      <line num="188" count="1" type="stmt"/>
      <line num="190" count="3" type="stmt"/>
      <line num="191" count="3" type="stmt"/>
      <line num="193" count="2" type="stmt"/>
      <line num="194" count="2" type="stmt"/>
      <line num="196" count="0" type="stmt"/>
      <line num="197" count="0" type="stmt"/>
      <line num="199" count="10" type="stmt"/>
      <line num="201" count="303" type="stmt"/>
      <line num="202" count="303" type="stmt"/>
      <line num="205" count="32" type="cond" truecount="1" falsecount="0"/>
      <line num="206" count="31" type="stmt"/>
      <line num="209" count="32" type="stmt"/>
      <line num="210" count="32" type="stmt"/>
      <line num="213" count="18" type="stmt"/>
      <line num="214" count="18" type="stmt"/>
      <line num="216" count="18" type="stmt"/>
      <line num="220" count="48" type="cond" truecount="1" falsecount="0"/>
      <line num="221" count="48" type="stmt"/>
      <line num="222" count="48" type="stmt"/>
      <line num="225" count="18" type="stmt"/>
      <line num="226" count="18" type="stmt"/>
      <line num="229" count="103" type="stmt"/>
      <line num="231" count="103" type="cond" truecount="2" falsecount="0"/>
      <line num="232" count="294" type="stmt"/>
      <line num="233" count="294" type="stmt"/>
      <line num="236" count="103" type="stmt"/>
      <line num="239" count="103" type="cond" truecount="6" falsecount="0"/>
      <line num="242" count="14" type="stmt"/>
      <line num="247" count="1" type="stmt"/>
      <line num="249" count="1" type="stmt"/>
      <line num="251" count="1" type="stmt"/>
      <line num="253" count="86" type="stmt"/>
      <line num="257" count="10" type="stmt"/>
      <line num="258" count="10" type="cond" truecount="1" falsecount="0"/>
      <line num="259" count="10" type="stmt"/>
      <line num="260" count="10" type="stmt"/>
      <line num="261" count="10" type="stmt"/>
      <line num="263" count="0" type="stmt"/>
      <line num="265" count="0" type="stmt"/>
      <line num="270" count="8" type="stmt"/>
      <line num="271" count="8" type="cond" truecount="1" falsecount="0"/>
      <line num="272" count="6" type="stmt"/>
      <line num="273" count="6" type="stmt"/>
      <line num="274" count="6" type="stmt"/>
      <line num="276" count="2" type="stmt"/>
      <line num="277" count="2" type="stmt"/>
      <line num="280" count="5" type="stmt"/>
      <line num="281" count="5" type="cond" truecount="1" falsecount="0"/>
      <line num="282" count="3" type="stmt"/>
      <line num="283" count="3" type="stmt"/>
      <line num="284" count="3" type="stmt"/>
      <line num="286" count="2" type="stmt"/>
      <line num="287" count="2" type="stmt"/>
      <line num="290" count="7" type="stmt"/>
      <line num="291" count="7" type="cond" truecount="1" falsecount="0"/>
      <line num="292" count="5" type="stmt"/>
      <line num="293" count="5" type="stmt"/>
      <line num="294" count="5" type="stmt"/>
      <line num="296" count="2" type="stmt"/>
      <line num="297" count="2" type="stmt"/>
      <line num="300" count="7" type="stmt"/>
      <line num="301" count="7" type="cond" truecount="1" falsecount="0"/>
      <line num="302" count="7" type="stmt"/>
      <line num="303" count="7" type="stmt"/>
      <line num="304" count="7" type="stmt"/>
      <line num="306" count="0" type="stmt"/>
      <line num="308" count="0" type="stmt"/>
      <line num="313" count="5" type="stmt"/>
      <line num="314" count="5" type="cond" truecount="1" falsecount="0"/>
      <line num="315" count="5" type="stmt"/>
      <line num="316" count="5" type="stmt"/>
      <line num="317" count="5" type="stmt"/>
      <line num="319" count="0" type="stmt"/>
      <line num="321" count="0" type="stmt"/>
      <line num="328" count="92" type="stmt"/>
      <line num="336" count="0" type="stmt"/>
      <line num="337" count="0" type="stmt"/>
      <line num="339" count="0" type="stmt"/>
      <line num="340" count="0" type="stmt"/>
      <line num="341" count="0" type="stmt"/>
      <line num="344" count="0" type="stmt"/>
      <line num="345" count="0" type="stmt"/>
    </file>
    <file name="types.ts" path="/Users/<USER>/WebstormProjects/filter-dsl/src/types.ts">
      <metrics statements="22" coveredstatements="22" conditionals="2" coveredconditionals="2" methods="1" coveredmethods="1"/>
      <line num="1" count="1" type="cond" truecount="2" falsecount="0"/>
      <line num="3" count="1" type="stmt"/>
      <line num="4" count="1" type="stmt"/>
      <line num="5" count="1" type="stmt"/>
      <line num="6" count="1" type="stmt"/>
      <line num="9" count="1" type="stmt"/>
      <line num="10" count="1" type="stmt"/>
      <line num="11" count="1" type="stmt"/>
      <line num="12" count="1" type="stmt"/>
      <line num="15" count="1" type="stmt"/>
      <line num="16" count="1" type="stmt"/>
      <line num="17" count="1" type="stmt"/>
      <line num="18" count="1" type="stmt"/>
      <line num="19" count="1" type="stmt"/>
      <line num="20" count="1" type="stmt"/>
      <line num="23" count="1" type="stmt"/>
      <line num="24" count="1" type="stmt"/>
      <line num="25" count="1" type="stmt"/>
      <line num="28" count="1" type="stmt"/>
      <line num="29" count="1" type="stmt"/>
      <line num="32" count="1" type="stmt"/>
      <line num="33" count="1" type="stmt"/>
    </file>
  </project>
</coverage>
