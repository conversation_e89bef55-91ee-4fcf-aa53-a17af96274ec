{"/Users/<USER>/WebstormProjects/filter-dsl/src/errors.ts": {"path": "/Users/<USER>/WebstormProjects/filter-dsl/src/errors.ts", "statementMap": {"0": {"start": {"line": 26, "column": 4}, "end": {"line": 26, "column": 19}}, "1": {"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": 31}}, "2": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 13}}, "3": {"start": {"line": 44, "column": 4}, "end": {"line": 44, "column": 19}}, "4": {"start": {"line": 45, "column": 4}, "end": {"line": 45, "column": 30}}, "5": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 13}}, "6": {"start": {"line": 49, "column": 21}, "end": {"line": 58, "column": 1}}, "7": {"start": {"line": 50, "column": 2}, "end": {"line": 57, "column": 3}}, "8": {"start": {"line": 52, "column": 6}, "end": {"line": 52, "column": 36}}, "9": {"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": 37}}, "10": {"start": {"line": 56, "column": 6}, "end": {"line": 56, "column": 31}}, "11": {"start": {"line": 49, "column": 13}, "end": {"line": 49, "column": 21}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": 14}}, "loc": {"start": {"line": 25, "column": 29}, "end": {"line": 28, "column": 3}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 43, "column": 2}, "end": {"line": 43, "column": 14}}, "loc": {"start": {"line": 43, "column": 29}, "end": {"line": 46, "column": 3}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 49, "column": 21}, "end": {"line": 49, "column": 22}}, "loc": {"start": {"line": 49, "column": 69}, "end": {"line": 58, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 50, "column": 2}, "end": {"line": 57, "column": 3}}, "type": "switch", "locations": [{"start": {"line": 51, "column": 4}, "end": {"line": 52, "column": 36}}, {"start": {"line": 53, "column": 4}, "end": {"line": 54, "column": 37}}, {"start": {"line": 55, "column": 4}, "end": {"line": 56, "column": 31}}]}}, "s": {"0": 0, "1": 0, "2": 1, "3": 0, "4": 0, "5": 1, "6": 1, "7": 0, "8": 0, "9": 0, "10": 0, "11": 1}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0, 0]}}, "/Users/<USER>/WebstormProjects/filter-dsl/src/lexer.ts": {"path": "/Users/<USER>/WebstormProjects/filter-dsl/src/lexer.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 47}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 60}}, "2": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 5, "column": 2}, "end": {"line": 5, "column": null}}, "4": {"start": {"line": 6, "column": 2}, "end": {"line": 6, "column": null}}, "5": {"start": {"line": 7, "column": 2}, "end": {"line": 7, "column": null}}, "6": {"start": {"line": 8, "column": 2}, "end": {"line": 8, "column": null}}, "7": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": null}}, "8": {"start": {"line": 10, "column": 2}, "end": {"line": 10, "column": null}}, "9": {"start": {"line": 11, "column": 2}, "end": {"line": 11, "column": null}}, "10": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": null}}, "11": {"start": {"line": 13, "column": 2}, "end": {"line": 13, "column": null}}, "12": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": null}}, "13": {"start": {"line": 23, "column": 31}, "end": {"line": 23, "column": 44}}, "14": {"start": {"line": 24, "column": 4}, "end": {"line": 24, "column": 22}}, "15": {"start": {"line": 25, "column": 4}, "end": {"line": 25, "column": 37}}, "16": {"start": {"line": 26, "column": 4}, "end": {"line": 26, "column": 39}}, "17": {"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": 24}}, "18": {"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 20}}, "19": {"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 51}}, "20": {"start": {"line": 36, "column": 4}, "end": {"line": 36, "column": 52}}, "21": {"start": {"line": 41, "column": 6}, "end": {"line": 43, "column": 58}}, "22": {"start": {"line": 44, "column": 4}, "end": {"line": 49, "column": 6}}, "23": {"start": {"line": 53, "column": 4}, "end": {"line": 55, "column": 5}}, "24": {"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": 21}}, "25": {"start": {"line": 56, "column": 4}, "end": {"line": 56, "column": 36}}, "26": {"start": {"line": 60, "column": 4}, "end": {"line": 60, "column": 34}}, "27": {"start": {"line": 64, "column": 4}, "end": {"line": 64, "column": 37}}, "28": {"start": {"line": 68, "column": 4}, "end": {"line": 68, "column": 30}}, "29": {"start": {"line": 76, "column": 4}, "end": {"line": 325, "column": 5}}, "30": {"start": {"line": 77, "column": 6}, "end": {"line": 324, "column": 7}}, "31": {"start": {"line": 79, "column": 10}, "end": {"line": 79, "column": 32}}, "32": {"start": {"line": 80, "column": 10}, "end": {"line": 80, "column": 35}}, "33": {"start": {"line": 80, "column": 29}, "end": {"line": 80, "column": 35}}, "34": {"start": {"line": 82, "column": 10}, "end": {"line": 82, "column": 42}}, "35": {"start": {"line": 85, "column": 10}, "end": {"line": 110, "column": 11}}, "36": {"start": {"line": 87, "column": 14}, "end": {"line": 87, "column": 29}}, "37": {"start": {"line": 88, "column": 14}, "end": {"line": 88, "column": 57}}, "38": {"start": {"line": 90, "column": 14}, "end": {"line": 90, "column": 29}}, "39": {"start": {"line": 91, "column": 14}, "end": {"line": 91, "column": 58}}, "40": {"start": {"line": 93, "column": 14}, "end": {"line": 93, "column": 29}}, "41": {"start": {"line": 94, "column": 14}, "end": {"line": 94, "column": 61}}, "42": {"start": {"line": 96, "column": 14}, "end": {"line": 96, "column": 29}}, "43": {"start": {"line": 97, "column": 14}, "end": {"line": 97, "column": 59}}, "44": {"start": {"line": 99, "column": 14}, "end": {"line": 99, "column": 29}}, "45": {"start": {"line": 100, "column": 14}, "end": {"line": 100, "column": 59}}, "46": {"start": {"line": 102, "column": 14}, "end": {"line": 102, "column": 29}}, "47": {"start": {"line": 103, "column": 14}, "end": {"line": 103, "column": 59}}, "48": {"start": {"line": 105, "column": 14}, "end": {"line": 105, "column": 29}}, "49": {"start": {"line": 106, "column": 14}, "end": {"line": 106, "column": 56}}, "50": {"start": {"line": 108, "column": 14}, "end": {"line": 108, "column": 29}}, "51": {"start": {"line": 109, "column": 14}, "end": {"line": 109, "column": 56}}, "52": {"start": {"line": 113, "column": 10}, "end": {"line": 117, "column": 11}}, "53": {"start": {"line": 114, "column": 12}, "end": {"line": 114, "column": 46}}, "54": {"start": {"line": 115, "column": 12}, "end": {"line": 115, "column": 27}}, "55": {"start": {"line": 116, "column": 12}, "end": {"line": 116, "column": 21}}, "56": {"start": {"line": 119, "column": 10}, "end": {"line": 122, "column": 11}}, "57": {"start": {"line": 120, "column": 12}, "end": {"line": 120, "column": 46}}, "58": {"start": {"line": 121, "column": 12}, "end": {"line": 121, "column": 21}}, "59": {"start": {"line": 124, "column": 10}, "end": {"line": 127, "column": 11}}, "60": {"start": {"line": 125, "column": 12}, "end": {"line": 125, "column": 45}}, "61": {"start": {"line": 126, "column": 12}, "end": {"line": 126, "column": 21}}, "62": {"start": {"line": 130, "column": 10}, "end": {"line": 133, "column": 11}}, "63": {"start": {"line": 131, "column": 12}, "end": {"line": 131, "column": 46}}, "64": {"start": {"line": 132, "column": 12}, "end": {"line": 132, "column": 21}}, "65": {"start": {"line": 135, "column": 10}, "end": {"line": 138, "column": 11}}, "66": {"start": {"line": 136, "column": 12}, "end": {"line": 136, "column": 43}}, "67": {"start": {"line": 137, "column": 12}, "end": {"line": 137, "column": 21}}, "68": {"start": {"line": 140, "column": 10}, "end": {"line": 143, "column": 11}}, "69": {"start": {"line": 141, "column": 12}, "end": {"line": 141, "column": 44}}, "70": {"start": {"line": 142, "column": 12}, "end": {"line": 142, "column": 21}}, "71": {"start": {"line": 145, "column": 10}, "end": {"line": 148, "column": 11}}, "72": {"start": {"line": 146, "column": 12}, "end": {"line": 146, "column": 47}}, "73": {"start": {"line": 147, "column": 12}, "end": {"line": 147, "column": 21}}, "74": {"start": {"line": 150, "column": 10}, "end": {"line": 153, "column": 11}}, "75": {"start": {"line": 151, "column": 12}, "end": {"line": 151, "column": 43}}, "76": {"start": {"line": 152, "column": 12}, "end": {"line": 152, "column": 21}}, "77": {"start": {"line": 155, "column": 10}, "end": {"line": 158, "column": 11}}, "78": {"start": {"line": 156, "column": 12}, "end": {"line": 156, "column": 42}}, "79": {"start": {"line": 157, "column": 12}, "end": {"line": 157, "column": 21}}, "80": {"start": {"line": 161, "column": 10}, "end": {"line": 161, "column": 25}}, "81": {"start": {"line": 163, "column": 10}, "end": {"line": 165, "column": 12}}, "82": {"start": {"line": 168, "column": 24}, "end": {"line": 168, "column": 51}}, "83": {"start": {"line": 169, "column": 28}, "end": {"line": 169, "column": 30}}, "84": {"start": {"line": 171, "column": 10}, "end": {"line": 203, "column": 11}}, "85": {"start": {"line": 172, "column": 12}, "end": {"line": 200, "column": 13}}, "86": {"start": {"line": 173, "column": 14}, "end": {"line": 173, "column": 30}}, "87": {"start": {"line": 174, "column": 14}, "end": {"line": 174, "column": 61}}, "88": {"start": {"line": 176, "column": 14}, "end": {"line": 198, "column": 15}}, "89": {"start": {"line": 178, "column": 18}, "end": {"line": 178, "column": 38}}, "90": {"start": {"line": 179, "column": 18}, "end": {"line": 179, "column": 24}}, "91": {"start": {"line": 181, "column": 18}, "end": {"line": 181, "column": 38}}, "92": {"start": {"line": 182, "column": 18}, "end": {"line": 182, "column": 24}}, "93": {"start": {"line": 184, "column": 18}, "end": {"line": 184, "column": 38}}, "94": {"start": {"line": 185, "column": 18}, "end": {"line": 185, "column": 24}}, "95": {"start": {"line": 187, "column": 18}, "end": {"line": 187, "column": 38}}, "96": {"start": {"line": 188, "column": 18}, "end": {"line": 188, "column": 24}}, "97": {"start": {"line": 190, "column": 18}, "end": {"line": 190, "column": 37}}, "98": {"start": {"line": 191, "column": 18}, "end": {"line": 191, "column": 24}}, "99": {"start": {"line": 193, "column": 18}, "end": {"line": 193, "column": 37}}, "100": {"start": {"line": 194, "column": 18}, "end": {"line": 194, "column": 24}}, "101": {"start": {"line": 196, "column": 18}, "end": {"line": 196, "column": 46}}, "102": {"start": {"line": 197, "column": 18}, "end": {"line": 197, "column": 24}}, "103": {"start": {"line": 199, "column": 14}, "end": {"line": 199, "column": 29}}, "104": {"start": {"line": 201, "column": 12}, "end": {"line": 201, "column": 40}}, "105": {"start": {"line": 202, "column": 12}, "end": {"line": 202, "column": 27}}, "106": {"start": {"line": 205, "column": 10}, "end": {"line": 207, "column": 11}}, "107": {"start": {"line": 206, "column": 12}, "end": {"line": 206, "column": 27}}, "108": {"start": {"line": 209, "column": 10}, "end": {"line": 209, "column": 43}}, "109": {"start": {"line": 210, "column": 10}, "end": {"line": 210, "column": 68}}, "110": {"start": {"line": 213, "column": 28}, "end": {"line": 213, "column": 30}}, "111": {"start": {"line": 214, "column": 23}, "end": {"line": 214, "column": 28}}, "112": {"start": {"line": 216, "column": 10}, "end": {"line": 223, "column": 11}}, "113": {"start": {"line": 220, "column": 12}, "end": {"line": 220, "column": 52}}, "114": {"start": {"line": 220, "column": 38}, "end": {"line": 220, "column": 52}}, "115": {"start": {"line": 221, "column": 12}, "end": {"line": 221, "column": 40}}, "116": {"start": {"line": 222, "column": 12}, "end": {"line": 222, "column": 27}}, "117": {"start": {"line": 225, "column": 10}, "end": {"line": 225, "column": 43}}, "118": {"start": {"line": 226, "column": 10}, "end": {"line": 226, "column": 68}}, "119": {"start": {"line": 229, "column": 27}, "end": {"line": 229, "column": 29}}, "120": {"start": {"line": 231, "column": 10}, "end": {"line": 234, "column": 11}}, "121": {"start": {"line": 232, "column": 12}, "end": {"line": 232, "column": 39}}, "122": {"start": {"line": 233, "column": 12}, "end": {"line": 233, "column": 27}}, "123": {"start": {"line": 236, "column": 10}, "end": {"line": 236, "column": 43}}, "124": {"start": {"line": 239, "column": 10}, "end": {"line": 254, "column": 11}}, "125": {"start": {"line": 242, "column": 14}, "end": {"line": 245, "column": 16}}, "126": {"start": {"line": 247, "column": 14}, "end": {"line": 247, "column": 62}}, "127": {"start": {"line": 249, "column": 14}, "end": {"line": 249, "column": 61}}, "128": {"start": {"line": 251, "column": 14}, "end": {"line": 251, "column": 61}}, "129": {"start": {"line": 253, "column": 14}, "end": {"line": 253, "column": 70}}, "130": {"start": {"line": 257, "column": 10}, "end": {"line": 257, "column": 25}}, "131": {"start": {"line": 258, "column": 10}, "end": {"line": 262, "column": 11}}, "132": {"start": {"line": 259, "column": 12}, "end": {"line": 259, "column": 27}}, "133": {"start": {"line": 260, "column": 12}, "end": {"line": 260, "column": 45}}, "134": {"start": {"line": 261, "column": 12}, "end": {"line": 261, "column": 63}}, "135": {"start": {"line": 263, "column": 10}, "end": {"line": 263, "column": 43}}, "136": {"start": {"line": 265, "column": 10}, "end": {"line": 267, "column": 12}}, "137": {"start": {"line": 270, "column": 10}, "end": {"line": 270, "column": 25}}, "138": {"start": {"line": 271, "column": 10}, "end": {"line": 275, "column": 11}}, "139": {"start": {"line": 272, "column": 12}, "end": {"line": 272, "column": 27}}, "140": {"start": {"line": 273, "column": 12}, "end": {"line": 273, "column": 45}}, "141": {"start": {"line": 274, "column": 12}, "end": {"line": 274, "column": 67}}, "142": {"start": {"line": 276, "column": 10}, "end": {"line": 276, "column": 43}}, "143": {"start": {"line": 277, "column": 10}, "end": {"line": 277, "column": 57}}, "144": {"start": {"line": 280, "column": 10}, "end": {"line": 280, "column": 25}}, "145": {"start": {"line": 281, "column": 10}, "end": {"line": 285, "column": 11}}, "146": {"start": {"line": 282, "column": 12}, "end": {"line": 282, "column": 27}}, "147": {"start": {"line": 283, "column": 12}, "end": {"line": 283, "column": 45}}, "148": {"start": {"line": 284, "column": 12}, "end": {"line": 284, "column": 67}}, "149": {"start": {"line": 286, "column": 10}, "end": {"line": 286, "column": 43}}, "150": {"start": {"line": 287, "column": 10}, "end": {"line": 287, "column": 63}}, "151": {"start": {"line": 290, "column": 10}, "end": {"line": 290, "column": 25}}, "152": {"start": {"line": 291, "column": 10}, "end": {"line": 295, "column": 11}}, "153": {"start": {"line": 292, "column": 12}, "end": {"line": 292, "column": 27}}, "154": {"start": {"line": 293, "column": 12}, "end": {"line": 293, "column": 45}}, "155": {"start": {"line": 294, "column": 12}, "end": {"line": 294, "column": 70}}, "156": {"start": {"line": 296, "column": 10}, "end": {"line": 296, "column": 43}}, "157": {"start": {"line": 297, "column": 10}, "end": {"line": 297, "column": 66}}, "158": {"start": {"line": 300, "column": 10}, "end": {"line": 300, "column": 25}}, "159": {"start": {"line": 301, "column": 10}, "end": {"line": 305, "column": 11}}, "160": {"start": {"line": 302, "column": 12}, "end": {"line": 302, "column": 27}}, "161": {"start": {"line": 303, "column": 12}, "end": {"line": 303, "column": 45}}, "162": {"start": {"line": 304, "column": 12}, "end": {"line": 304, "column": 60}}, "163": {"start": {"line": 306, "column": 10}, "end": {"line": 306, "column": 43}}, "164": {"start": {"line": 308, "column": 10}, "end": {"line": 310, "column": 12}}, "165": {"start": {"line": 313, "column": 10}, "end": {"line": 313, "column": 25}}, "166": {"start": {"line": 314, "column": 10}, "end": {"line": 318, "column": 11}}, "167": {"start": {"line": 315, "column": 12}, "end": {"line": 315, "column": 27}}, "168": {"start": {"line": 316, "column": 12}, "end": {"line": 316, "column": 45}}, "169": {"start": {"line": 317, "column": 12}, "end": {"line": 317, "column": 59}}, "170": {"start": {"line": 319, "column": 10}, "end": {"line": 319, "column": 43}}, "171": {"start": {"line": 321, "column": 10}, "end": {"line": 323, "column": 12}}, "172": {"start": {"line": 328, "column": 4}, "end": {"line": 328, "column": 50}}, "173": {"start": {"line": 336, "column": 34}, "end": {"line": 336, "column": 36}}, "174": {"start": {"line": 337, "column": 16}, "end": {"line": 337, "column": 32}}, "175": {"start": {"line": 339, "column": 4}, "end": {"line": 342, "column": 5}}, "176": {"start": {"line": 340, "column": 6}, "end": {"line": 340, "column": 25}}, "177": {"start": {"line": 341, "column": 6}, "end": {"line": 341, "column": 31}}, "178": {"start": {"line": 344, "column": 4}, "end": {"line": 344, "column": 23}}, "179": {"start": {"line": 345, "column": 4}, "end": {"line": 345, "column": 18}}, "180": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 13}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 5}}, "loc": {"start": {"line": 4, "column": 18}, "end": {"line": 15, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 31}}, "loc": {"start": {"line": 23, "column": 44}, "end": {"line": 28, "column": 3}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 30, "column": 10}, "end": {"line": 30, "column": 17}}, "loc": {"start": {"line": 30, "column": 17}, "end": {"line": 33, "column": 3}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 35, "column": 10}, "end": {"line": 35, "column": 14}}, "loc": {"start": {"line": 35, "column": 25}, "end": {"line": 37, "column": 3}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 39, "column": 10}, "end": {"line": 39, "column": 21}}, "loc": {"start": {"line": 39, "column": 56}, "end": {"line": 50, "column": 3}}}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 52, "column": 10}, "end": {"line": 52, "column": 24}}, "loc": {"start": {"line": 52, "column": 24}, "end": {"line": 57, "column": 3}}}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 59, "column": 10}, "end": {"line": 59, "column": 17}}, "loc": {"start": {"line": 59, "column": 30}, "end": {"line": 61, "column": 3}}}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 63, "column": 10}, "end": {"line": 63, "column": 20}}, "loc": {"start": {"line": 63, "column": 33}, "end": {"line": 65, "column": 3}}}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 67, "column": 10}, "end": {"line": 67, "column": 17}}, "loc": {"start": {"line": 67, "column": 30}, "end": {"line": 69, "column": 3}}}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 75, "column": 9}, "end": {"line": 75, "column": 18}}, "loc": {"start": {"line": 75, "column": 18}, "end": {"line": 329, "column": 3}}}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 335, "column": 9}, "end": {"line": 335, "column": 17}}, "loc": {"start": {"line": 335, "column": 17}, "end": {"line": 346, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 4, "column": 5}, "end": {"line": 4, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 4, "column": 5}, "end": {"line": 4, "column": 18}}, {"start": {"line": 4, "column": 5}, "end": {"line": 4, "column": null}}]}, "1": {"loc": {"start": {"line": 26, "column": 19}, "end": {"line": 26, "column": 38}}, "type": "binary-expr", "locations": [{"start": {"line": 26, "column": 19}, "end": {"line": 26, "column": 32}}, {"start": {"line": 26, "column": 36}, "end": {"line": 26, "column": 38}}]}, "2": {"loc": {"start": {"line": 32, "column": 19}, "end": {"line": 32, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 32, "column": 19}, "end": {"line": 32, "column": 44}}, {"start": {"line": 32, "column": 48}, "end": {"line": 32, "column": 50}}]}, "3": {"loc": {"start": {"line": 35, "column": 15}, "end": {"line": 35, "column": 25}}, "type": "default-arg", "locations": [{"start": {"line": 35, "column": 24}, "end": {"line": 35, "column": 25}}]}, "4": {"loc": {"start": {"line": 36, "column": 11}, "end": {"line": 36, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 36, "column": 11}, "end": {"line": 36, "column": 45}}, {"start": {"line": 36, "column": 49}, "end": {"line": 36, "column": 51}}]}, "5": {"loc": {"start": {"line": 41, "column": 6}, "end": {"line": 43, "column": 58}}, "type": "cond-expr", "locations": [{"start": {"line": 42, "column": 10}, "end": {"line": 42, "column": 15}}, {"start": {"line": 43, "column": 10}, "end": {"line": 43, "column": 58}}]}, "6": {"loc": {"start": {"line": 53, "column": 11}, "end": {"line": 53, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 53, "column": 11}, "end": {"line": 53, "column": 23}}, {"start": {"line": 53, "column": 27}, "end": {"line": 53, "column": 50}}]}, "7": {"loc": {"start": {"line": 77, "column": 6}, "end": {"line": 324, "column": 7}}, "type": "switch", "locations": [{"start": {"line": 78, "column": 8}, "end": {"line": 165, "column": 12}}, {"start": {"line": 167, "column": 8}, "end": {"line": 210, "column": 68}}, {"start": {"line": 212, "column": 8}, "end": {"line": 226, "column": 68}}, {"start": {"line": 228, "column": 8}, "end": {"line": 254, "column": 11}}, {"start": {"line": 256, "column": 8}, "end": {"line": 267, "column": 12}}, {"start": {"line": 269, "column": 8}, "end": {"line": 277, "column": 57}}, {"start": {"line": 279, "column": 8}, "end": {"line": 287, "column": 63}}, {"start": {"line": 289, "column": 8}, "end": {"line": 297, "column": 66}}, {"start": {"line": 299, "column": 8}, "end": {"line": 310, "column": 12}}, {"start": {"line": 312, "column": 8}, "end": {"line": 323, "column": 12}}]}, "8": {"loc": {"start": {"line": 80, "column": 10}, "end": {"line": 80, "column": 35}}, "type": "if", "locations": [{"start": {"line": 80, "column": 10}, "end": {"line": 80, "column": 35}}]}, "9": {"loc": {"start": {"line": 85, "column": 10}, "end": {"line": 110, "column": 11}}, "type": "switch", "locations": [{"start": {"line": 86, "column": 12}, "end": {"line": 88, "column": 57}}, {"start": {"line": 89, "column": 12}, "end": {"line": 91, "column": 58}}, {"start": {"line": 92, "column": 12}, "end": {"line": 94, "column": 61}}, {"start": {"line": 95, "column": 12}, "end": {"line": 97, "column": 59}}, {"start": {"line": 98, "column": 12}, "end": {"line": 100, "column": 59}}, {"start": {"line": 101, "column": 12}, "end": {"line": 103, "column": 59}}, {"start": {"line": 104, "column": 12}, "end": {"line": 106, "column": 56}}, {"start": {"line": 107, "column": 12}, "end": {"line": 109, "column": 56}}]}, "10": {"loc": {"start": {"line": 113, "column": 10}, "end": {"line": 117, "column": 11}}, "type": "if", "locations": [{"start": {"line": 113, "column": 10}, "end": {"line": 117, "column": 11}}]}, "11": {"loc": {"start": {"line": 113, "column": 14}, "end": {"line": 113, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 113, "column": 14}, "end": {"line": 113, "column": 34}}, {"start": {"line": 113, "column": 38}, "end": {"line": 113, "column": 58}}]}, "12": {"loc": {"start": {"line": 119, "column": 10}, "end": {"line": 122, "column": 11}}, "type": "if", "locations": [{"start": {"line": 119, "column": 10}, "end": {"line": 122, "column": 11}}]}, "13": {"loc": {"start": {"line": 124, "column": 10}, "end": {"line": 127, "column": 11}}, "type": "if", "locations": [{"start": {"line": 124, "column": 10}, "end": {"line": 127, "column": 11}}]}, "14": {"loc": {"start": {"line": 130, "column": 10}, "end": {"line": 133, "column": 11}}, "type": "if", "locations": [{"start": {"line": 130, "column": 10}, "end": {"line": 133, "column": 11}}]}, "15": {"loc": {"start": {"line": 135, "column": 10}, "end": {"line": 138, "column": 11}}, "type": "if", "locations": [{"start": {"line": 135, "column": 10}, "end": {"line": 138, "column": 11}}]}, "16": {"loc": {"start": {"line": 140, "column": 10}, "end": {"line": 143, "column": 11}}, "type": "if", "locations": [{"start": {"line": 140, "column": 10}, "end": {"line": 143, "column": 11}}]}, "17": {"loc": {"start": {"line": 145, "column": 10}, "end": {"line": 148, "column": 11}}, "type": "if", "locations": [{"start": {"line": 145, "column": 10}, "end": {"line": 148, "column": 11}}]}, "18": {"loc": {"start": {"line": 150, "column": 10}, "end": {"line": 153, "column": 11}}, "type": "if", "locations": [{"start": {"line": 150, "column": 10}, "end": {"line": 153, "column": 11}}]}, "19": {"loc": {"start": {"line": 155, "column": 10}, "end": {"line": 158, "column": 11}}, "type": "if", "locations": [{"start": {"line": 155, "column": 10}, "end": {"line": 158, "column": 11}}]}, "20": {"loc": {"start": {"line": 171, "column": 17}, "end": {"line": 171, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 171, "column": 17}, "end": {"line": 171, "column": 29}}, {"start": {"line": 171, "column": 33}, "end": {"line": 171, "column": 55}}]}, "21": {"loc": {"start": {"line": 172, "column": 12}, "end": {"line": 200, "column": 13}}, "type": "if", "locations": [{"start": {"line": 172, "column": 12}, "end": {"line": 200, "column": 13}}]}, "22": {"loc": {"start": {"line": 174, "column": 29}, "end": {"line": 174, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 174, "column": 29}, "end": {"line": 174, "column": 54}}, {"start": {"line": 174, "column": 58}, "end": {"line": 174, "column": 60}}]}, "23": {"loc": {"start": {"line": 176, "column": 14}, "end": {"line": 198, "column": 15}}, "type": "switch", "locations": [{"start": {"line": 177, "column": 16}, "end": {"line": 179, "column": 24}}, {"start": {"line": 180, "column": 16}, "end": {"line": 182, "column": 24}}, {"start": {"line": 183, "column": 16}, "end": {"line": 185, "column": 24}}, {"start": {"line": 186, "column": 16}, "end": {"line": 188, "column": 24}}, {"start": {"line": 189, "column": 16}, "end": {"line": 191, "column": 24}}, {"start": {"line": 192, "column": 16}, "end": {"line": 194, "column": 24}}, {"start": {"line": 195, "column": 16}, "end": {"line": 197, "column": 24}}]}, "24": {"loc": {"start": {"line": 205, "column": 10}, "end": {"line": 207, "column": 11}}, "type": "if", "locations": [{"start": {"line": 205, "column": 10}, "end": {"line": 207, "column": 11}}]}, "25": {"loc": {"start": {"line": 217, "column": 12}, "end": {"line": 218, "column": 77}}, "type": "binary-expr", "locations": [{"start": {"line": 217, "column": 12}, "end": {"line": 217, "column": 24}}, {"start": {"line": 218, "column": 13}, "end": {"line": 218, "column": 39}}, {"start": {"line": 218, "column": 44}, "end": {"line": 218, "column": 64}}, {"start": {"line": 218, "column": 68}, "end": {"line": 218, "column": 75}}]}, "26": {"loc": {"start": {"line": 220, "column": 12}, "end": {"line": 220, "column": 52}}, "type": "if", "locations": [{"start": {"line": 220, "column": 12}, "end": {"line": 220, "column": 52}}]}, "27": {"loc": {"start": {"line": 231, "column": 17}, "end": {"line": 231, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 231, "column": 17}, "end": {"line": 231, "column": 29}}, {"start": {"line": 231, "column": 33}, "end": {"line": 231, "column": 62}}]}, "28": {"loc": {"start": {"line": 239, "column": 10}, "end": {"line": 254, "column": 11}}, "type": "switch", "locations": [{"start": {"line": 240, "column": 12}, "end": {"line": 240, "column": 24}}, {"start": {"line": 241, "column": 12}, "end": {"line": 245, "column": 16}}, {"start": {"line": 246, "column": 12}, "end": {"line": 247, "column": 62}}, {"start": {"line": 248, "column": 12}, "end": {"line": 249, "column": 61}}, {"start": {"line": 250, "column": 12}, "end": {"line": 251, "column": 61}}, {"start": {"line": 252, "column": 12}, "end": {"line": 253, "column": 70}}]}, "29": {"loc": {"start": {"line": 258, "column": 10}, "end": {"line": 262, "column": 11}}, "type": "if", "locations": [{"start": {"line": 258, "column": 10}, "end": {"line": 262, "column": 11}}]}, "30": {"loc": {"start": {"line": 271, "column": 10}, "end": {"line": 275, "column": 11}}, "type": "if", "locations": [{"start": {"line": 271, "column": 10}, "end": {"line": 275, "column": 11}}]}, "31": {"loc": {"start": {"line": 281, "column": 10}, "end": {"line": 285, "column": 11}}, "type": "if", "locations": [{"start": {"line": 281, "column": 10}, "end": {"line": 285, "column": 11}}]}, "32": {"loc": {"start": {"line": 291, "column": 10}, "end": {"line": 295, "column": 11}}, "type": "if", "locations": [{"start": {"line": 291, "column": 10}, "end": {"line": 295, "column": 11}}]}, "33": {"loc": {"start": {"line": 301, "column": 10}, "end": {"line": 305, "column": 11}}, "type": "if", "locations": [{"start": {"line": 301, "column": 10}, "end": {"line": 305, "column": 11}}]}, "34": {"loc": {"start": {"line": 314, "column": 10}, "end": {"line": 318, "column": 11}}, "type": "if", "locations": [{"start": {"line": 314, "column": 10}, "end": {"line": 318, "column": 11}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "12": 1, "13": 93, "14": 93, "15": 93, "16": 93, "17": 93, "18": 1023, "19": 1023, "20": 0, "21": 351, "22": 351, "23": 263, "24": 163, "25": 263, "26": 145, "27": 363, "28": 218, "29": 351, "30": 458, "31": 263, "32": 263, "33": 4, "34": 259, "35": 259, "36": 12, "37": 12, "38": 5, "39": 5, "40": 6, "41": 6, "42": 5, "43": 5, "44": 14, "45": 14, "46": 14, "47": 14, "48": 7, "49": 7, "50": 1, "51": 1, "52": 195, "53": 32, "54": 32, "55": 32, "56": 163, "57": 18, "58": 18, "59": 145, "60": 103, "61": 103, "62": 42, "63": 10, "64": 10, "65": 32, "66": 8, "67": 8, "68": 24, "69": 5, "70": 5, "71": 19, "72": 7, "73": 7, "74": 12, "75": 7, "76": 7, "77": 5, "78": 5, "79": 5, "80": 0, "81": 0, "82": 32, "83": 32, "84": 32, "85": 303, "86": 10, "87": 10, "88": 10, "89": 2, "90": 2, "91": 1, "92": 1, "93": 1, "94": 1, "95": 1, "96": 1, "97": 3, "98": 3, "99": 2, "100": 2, "101": 0, "102": 0, "103": 10, "104": 303, "105": 303, "106": 32, "107": 31, "108": 32, "109": 32, "110": 18, "111": 18, "112": 18, "113": 48, "114": 3, "115": 48, "116": 48, "117": 18, "118": 18, "119": 103, "120": 103, "121": 294, "122": 294, "123": 103, "124": 103, "125": 14, "126": 1, "127": 1, "128": 1, "129": 86, "130": 10, "131": 10, "132": 10, "133": 10, "134": 10, "135": 0, "136": 0, "137": 8, "138": 8, "139": 6, "140": 6, "141": 6, "142": 2, "143": 2, "144": 5, "145": 5, "146": 3, "147": 3, "148": 3, "149": 2, "150": 2, "151": 7, "152": 7, "153": 5, "154": 5, "155": 5, "156": 2, "157": 2, "158": 7, "159": 7, "160": 7, "161": 7, "162": 7, "163": 0, "164": 0, "165": 5, "166": 5, "167": 5, "168": 5, "169": 5, "170": 0, "171": 0, "172": 92, "173": 0, "174": 0, "175": 0, "176": 0, "177": 0, "178": 0, "179": 0, "180": 1}, "f": {"0": 1, "1": 93, "2": 1023, "3": 0, "4": 351, "5": 263, "6": 145, "7": 363, "8": 218, "9": 351, "10": 0}, "b": {"0": [1, 1], "1": [93, 0], "2": [1023, 93], "3": [0], "4": [0, 0], "5": [287, 64], "6": [426, 422], "7": [263, 32, 18, 103, 10, 8, 5, 7, 7, 5], "8": [4], "9": [12, 5, 6, 5, 14, 14, 7, 1], "10": [32], "11": [195, 182], "12": [18], "13": [103], "14": [10], "15": [8], "16": [5], "17": [7], "18": [7], "19": [5], "20": [335, 334], "21": [10], "22": [10, 0], "23": [2, 1, 1, 1, 3, 2, 0], "24": [31], "25": [66, 55, 10, 3], "26": [3], "27": [397, 363], "28": [7, 14, 1, 1, 1, 86], "29": [10], "30": [6], "31": [3], "32": [5], "33": [7], "34": [5]}}, "/Users/<USER>/WebstormProjects/filter-dsl/src/types.ts": {"path": "/Users/<USER>/WebstormProjects/filter-dsl/src/types.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 3, "column": 2}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 4, "column": 2}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 5, "column": 2}, "end": {"line": 5, "column": null}}, "4": {"start": {"line": 6, "column": 2}, "end": {"line": 6, "column": null}}, "5": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": null}}, "6": {"start": {"line": 10, "column": 2}, "end": {"line": 10, "column": null}}, "7": {"start": {"line": 11, "column": 2}, "end": {"line": 11, "column": null}}, "8": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": null}}, "9": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": null}}, "10": {"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": null}}, "11": {"start": {"line": 17, "column": 2}, "end": {"line": 17, "column": null}}, "12": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": null}}, "13": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": null}}, "14": {"start": {"line": 20, "column": 2}, "end": {"line": 20, "column": null}}, "15": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": null}}, "16": {"start": {"line": 24, "column": 2}, "end": {"line": 24, "column": null}}, "17": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": null}}, "18": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": null}}, "19": {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": null}}, "20": {"start": {"line": 32, "column": 2}, "end": {"line": 32, "column": null}}, "21": {"start": {"line": 33, "column": 2}, "end": {"line": 33, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 12}}, "loc": {"start": {"line": 1, "column": 24}, "end": {"line": 34, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 1, "column": 12}, "end": {"line": 1, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 1, "column": 12}, "end": {"line": 1, "column": 24}}, {"start": {"line": 1, "column": 24}, "end": {"line": 1, "column": null}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "12": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "19": 1, "20": 1, "21": 1}, "f": {"0": 1}, "b": {"0": [1, 1]}}}