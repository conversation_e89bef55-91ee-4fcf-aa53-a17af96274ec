import { E_TOKEN_TYPE, TToken } from "../src/types";
import { entries, map } from "lodash";
import { QueryLexer } from "../src/lexer";

describe("Lexer tests", () => {
  const check = (input: string, expected: Array<Partial<TToken>>): void => {
    const lexicalAnalyser = new QueryLexer(input);
    let token: TToken = lexicalAnalyser.nextToken();
    let i = 0;

    while (token.type !== E_TOKEN_TYPE.EOF) {
      const expectedToken = expected[i];

      if (expectedToken === undefined)
        // eslint-disable-next-line prettier/prettier
        throw new Error('Expected token doesn\'t exist');

      expect(token).toEqual(
        expect.objectContaining<Partial<TToken>>({
          ...expected[i],
        }),
      );

      token = lexicalAnalyser.nextToken();
      i += 1;
    }
  };

  const testsSets: Record<string, Record<string, Array<Partial<TToken>>>> = {
    Empty: {
      "     ": [],
      "\t  \t \n\r\r   ": [],
      "  \n\n\r\t\t\0": [{ type: E_TOKEN_TYPE.EOF, value: "" }],
    },
    Brackets: {
      "(": [{ type: E_TOKEN_TYPE.LPAREN, value: "(" }],
      ")": [{ type: E_TOKEN_TYPE.RPAREN, value: ")" }],
      "(()())": [
        { type: E_TOKEN_TYPE.LPAREN, value: "(" },
        { type: E_TOKEN_TYPE.LPAREN, value: "(" },
        { type: E_TOKEN_TYPE.RPAREN, value: ")" },
        { type: E_TOKEN_TYPE.LPAREN, value: "(" },
        { type: E_TOKEN_TYPE.RPAREN, value: ")" },
        { type: E_TOKEN_TYPE.RPAREN, value: ")" },
      ],
    },
    Numbers: {
      "123": [{ type: E_TOKEN_TYPE.NUMBER, value: "123" }],
      "1234": [{ type: E_TOKEN_TYPE.NUMBER, value: "1234" }],
      "123 456": [
        { type: E_TOKEN_TYPE.NUMBER, value: "123" },
        { type: E_TOKEN_TYPE.NUMBER, value: "456" },
      ],
      "123\t\n  456": [
        { type: E_TOKEN_TYPE.NUMBER, value: "123" },
        { type: E_TOKEN_TYPE.NUMBER, value: "456" },
      ],
    },
    Operators: {
      "+": [{ type: E_TOKEN_TYPE.PLUS, value: "+" }],
      "-": [{ type: E_TOKEN_TYPE.MINUS, value: "-" }],
      "*": [{ type: E_TOKEN_TYPE.MULTIPLY, value: "*" }],
      "/": [{ type: E_TOKEN_TYPE.DIVIDE, value: "/" }],
      "==": [{ type: E_TOKEN_TYPE.EQUALS, value: "==" }],
      "!=": [{ type: E_TOKEN_TYPE.NOT_EQUALS, value: "!=" }],
      "<": [{ type: E_TOKEN_TYPE.LESS_THAN, value: "<" }],
      ">": [{ type: E_TOKEN_TYPE.GREATER_THAN, value: ">" }],
      "<=": [{ type: E_TOKEN_TYPE.LESS_EQUAL, value: "<=" }],
      ">=": [{ type: E_TOKEN_TYPE.GREATER_EQUAL, value: ">=" }],
    },
    Identifiers: {
      a: [{ type: E_TOKEN_TYPE.FIELD, value: "a" }],
      a1: [{ type: E_TOKEN_TYPE.FIELD, value: "a1" }],
      "a.b": [
        { type: E_TOKEN_TYPE.FIELD, value: "a" },
        { type: E_TOKEN_TYPE.DOT, value: "." },
        { type: E_TOKEN_TYPE.FIELD, value: "b" },
      ],
    },
    Sequences: {
      "a + 12": [
        { type: E_TOKEN_TYPE.FIELD, value: "a" },
        { type: E_TOKEN_TYPE.PLUS, value: "+" },
        { type: E_TOKEN_TYPE.NUMBER, value: "12" },
      ],
    },
  };

  map(entries(testsSets), ([setName, tests]) => {
    describe(setName, () => {
      map(entries(tests), ([input, expected]) => {
        test(input, () => {
          check(input, expected);
        });
      });
    });
  });
});
