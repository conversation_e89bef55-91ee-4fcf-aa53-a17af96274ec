import { E_TOKEN_TYPE, TToken } from "../src/types";
import { entries, map } from "lodash";
import { QueryLexer } from "../src/lexer";

describe("Lexer tests", () => {
  const check = (input: string, expected: Array<Partial<TToken>>): void => {
    const lexicalAnalyser = new QueryLexer(input);
    let token: TToken = lexicalAnalyser.nextToken();
    let i = 0;

    while (token.type !== E_TOKEN_TYPE.EOF) {
      const expectedToken = expected[i];

      if (expectedToken === undefined)
        // eslint-disable-next-line prettier/prettier
        throw new Error('Expected token doesn\'t exist');

      expect(token).toEqual(
        expect.objectContaining<Partial<TToken>>({
          ...expected[i],
        }),
      );

      token = lexicalAnalyser.nextToken();
      i += 1;
    }
  };

  const testsSets: Record<string, Record<string, Array<Partial<TToken>>>> = {
    Empty: {
      "     ": [],
      "\t  \t \n\r\r   ": [],
      "  \n\n\r\t\t\0": [{ type: E_TOKEN_TYPE.EOF, value: "" }],
    },
    Brackets: {
      "(": [{ type: E_TOKEN_TYPE.LPAREN, value: "(" }],
      ")": [{ type: E_TOKEN_TYPE.RPAREN, value: ")" }],
      "(()())": [
        { type: E_TOKEN_TYPE.LPAREN, value: "(" },
        { type: E_TOKEN_TYPE.LPAREN, value: "(" },
        { type: E_TOKEN_TYPE.RPAREN, value: ")" },
        { type: E_TOKEN_TYPE.LPAREN, value: "(" },
        { type: E_TOKEN_TYPE.RPAREN, value: ")" },
        { type: E_TOKEN_TYPE.RPAREN, value: ")" },
      ],
    },
    Numbers: {
      "123": [{ type: E_TOKEN_TYPE.NUMBER, value: "123" }],
      "1234": [{ type: E_TOKEN_TYPE.NUMBER, value: "1234" }],
      "123 456": [
        { type: E_TOKEN_TYPE.NUMBER, value: "123" },
        { type: E_TOKEN_TYPE.NUMBER, value: "456" },
      ],
      "123\t\n  456": [
        { type: E_TOKEN_TYPE.NUMBER, value: "123" },
        { type: E_TOKEN_TYPE.NUMBER, value: "456" },
      ],
    },
    Operators: {
      "+": [{ type: E_TOKEN_TYPE.PLUS, value: "+" }],
      "-": [{ type: E_TOKEN_TYPE.MINUS, value: "-" }],
      "*": [{ type: E_TOKEN_TYPE.MULTIPLY, value: "*" }],
      "/": [{ type: E_TOKEN_TYPE.DIVIDE, value: "/" }],
      "==": [{ type: E_TOKEN_TYPE.EQUALS, value: "==" }],
      "!=": [{ type: E_TOKEN_TYPE.NOT_EQUALS, value: "!=" }],
      "<": [{ type: E_TOKEN_TYPE.LESS_THAN, value: "<" }],
      ">": [{ type: E_TOKEN_TYPE.GREATER_THAN, value: ">" }],
      "<=": [{ type: E_TOKEN_TYPE.LESS_EQUAL, value: "<=" }],
      ">=": [{ type: E_TOKEN_TYPE.GREATER_EQUAL, value: ">=" }],
    },
    Identifiers: {
      a: [{ type: E_TOKEN_TYPE.FIELD, value: "a" }],
      a1: [{ type: E_TOKEN_TYPE.FIELD, value: "a1" }],
      "a.b": [
        { type: E_TOKEN_TYPE.FIELD, value: "a" },
        { type: E_TOKEN_TYPE.DOT, value: "." },
        { type: E_TOKEN_TYPE.FIELD, value: "b" },
      ],
    },
    Sequences: {
      "a + 12": [
        { type: E_TOKEN_TYPE.FIELD, value: "a" },
        { type: E_TOKEN_TYPE.PLUS, value: "+" },
        { type: E_TOKEN_TYPE.NUMBER, value: "12" },
      ],
      "a + b - c": [
        { type: E_TOKEN_TYPE.FIELD, value: "a" },
        { type: E_TOKEN_TYPE.PLUS, value: "+" },
        { type: E_TOKEN_TYPE.FIELD, value: "b" },
        { type: E_TOKEN_TYPE.MINUS, value: "-" },
        { type: E_TOKEN_TYPE.FIELD, value: "c" },
      ],
      "x * 2 / 3": [
        { type: E_TOKEN_TYPE.FIELD, value: "x" },
        { type: E_TOKEN_TYPE.MULTIPLY, value: "*" },
        { type: E_TOKEN_TYPE.NUMBER, value: "2" },
        { type: E_TOKEN_TYPE.DIVIDE, value: "/" },
        { type: E_TOKEN_TYPE.NUMBER, value: "3" },
      ],
      // Comparison expressions
      "age >= 18": [
        { type: E_TOKEN_TYPE.FIELD, value: "age" },
        { type: E_TOKEN_TYPE.GREATER_EQUAL, value: ">=" },
        { type: E_TOKEN_TYPE.NUMBER, value: "18" },
      ],
      "name == 'John'": [
        { type: E_TOKEN_TYPE.FIELD, value: "name" },
        { type: E_TOKEN_TYPE.EQUALS, value: "==" },
        { type: E_TOKEN_TYPE.STRING, value: "John" },
      ],
      "status != 'active'": [
        { type: E_TOKEN_TYPE.FIELD, value: "status" },
        { type: E_TOKEN_TYPE.NOT_EQUALS, value: "!=" },
        { type: E_TOKEN_TYPE.STRING, value: "active" },
      ],
      "score < 100": [
        { type: E_TOKEN_TYPE.FIELD, value: "score" },
        { type: E_TOKEN_TYPE.LESS_THAN, value: "<" },
        { type: E_TOKEN_TYPE.NUMBER, value: "100" },
      ],
      "price <= 50.99": [
        { type: E_TOKEN_TYPE.FIELD, value: "price" },
        { type: E_TOKEN_TYPE.LESS_EQUAL, value: "<=" },
        { type: E_TOKEN_TYPE.NUMBER, value: "50.99" },
      ],
      // Logical expressions
      "a && b": [
        { type: E_TOKEN_TYPE.FIELD, value: "a" },
        { type: E_TOKEN_TYPE.AND, value: "&&" },
        { type: E_TOKEN_TYPE.FIELD, value: "b" },
      ],
      "x || y": [
        { type: E_TOKEN_TYPE.FIELD, value: "x" },
        { type: E_TOKEN_TYPE.OR, value: "||" },
        { type: E_TOKEN_TYPE.FIELD, value: "y" },
      ],
      "!active": [
        { type: E_TOKEN_TYPE.NOT, value: "!" },
        { type: E_TOKEN_TYPE.FIELD, value: "active" },
      ],
      // Keyword logical operators
      "a and b": [
        { type: E_TOKEN_TYPE.FIELD, value: "a" },
        { type: E_TOKEN_TYPE.AND, value: "&&" },
        { type: E_TOKEN_TYPE.FIELD, value: "b" },
      ],
      "x or y": [
        { type: E_TOKEN_TYPE.FIELD, value: "x" },
        { type: E_TOKEN_TYPE.OR, value: "||" },
        { type: E_TOKEN_TYPE.FIELD, value: "y" },
      ],
      "not active": [
        { type: E_TOKEN_TYPE.NOT, value: "!" },
        { type: E_TOKEN_TYPE.FIELD, value: "active" },
      ],
      // Boolean literals
      "isActive == true": [
        { type: E_TOKEN_TYPE.FIELD, value: "isActive" },
        { type: E_TOKEN_TYPE.EQUALS, value: "==" },
        { type: E_TOKEN_TYPE.BOOLEAN, value: "true" },
      ],
      "isDeleted != false": [
        { type: E_TOKEN_TYPE.FIELD, value: "isDeleted" },
        { type: E_TOKEN_TYPE.NOT_EQUALS, value: "!=" },
        { type: E_TOKEN_TYPE.BOOLEAN, value: "false" },
      ],
      // Object path expressions
      "user.name == 'Alice'": [
        { type: E_TOKEN_TYPE.FIELD, value: "user" },
        { type: E_TOKEN_TYPE.DOT, value: "." },
        { type: E_TOKEN_TYPE.FIELD, value: "name" },
        { type: E_TOKEN_TYPE.EQUALS, value: "==" },
        { type: E_TOKEN_TYPE.STRING, value: "Alice" },
      ],
      "order.items.count > 0": [
        { type: E_TOKEN_TYPE.FIELD, value: "order" },
        { type: E_TOKEN_TYPE.DOT, value: "." },
        { type: E_TOKEN_TYPE.FIELD, value: "items" },
        { type: E_TOKEN_TYPE.DOT, value: "." },
        { type: E_TOKEN_TYPE.FIELD, value: "count" },
        { type: E_TOKEN_TYPE.GREATER_THAN, value: ">" },
        { type: E_TOKEN_TYPE.NUMBER, value: "0" },
      ],
      // Parenthesized expressions
      "(a + b) * c": [
        { type: E_TOKEN_TYPE.LPAREN, value: "(" },
        { type: E_TOKEN_TYPE.FIELD, value: "a" },
        { type: E_TOKEN_TYPE.PLUS, value: "+" },
        { type: E_TOKEN_TYPE.FIELD, value: "b" },
        { type: E_TOKEN_TYPE.RPAREN, value: ")" },
        { type: E_TOKEN_TYPE.MULTIPLY, value: "*" },
        { type: E_TOKEN_TYPE.FIELD, value: "c" },
      ],
      "(age >= 18) && (status == 'active')": [
        { type: E_TOKEN_TYPE.LPAREN, value: "(" },
        { type: E_TOKEN_TYPE.FIELD, value: "age" },
        { type: E_TOKEN_TYPE.GREATER_EQUAL, value: ">=" },
        { type: E_TOKEN_TYPE.NUMBER, value: "18" },
        { type: E_TOKEN_TYPE.RPAREN, value: ")" },
        { type: E_TOKEN_TYPE.AND, value: "&&" },
        { type: E_TOKEN_TYPE.LPAREN, value: "(" },
        { type: E_TOKEN_TYPE.FIELD, value: "status" },
        { type: E_TOKEN_TYPE.EQUALS, value: "==" },
        { type: E_TOKEN_TYPE.STRING, value: "active" },
        { type: E_TOKEN_TYPE.RPAREN, value: ")" },
      ],
    },
    "Complex Sequences": {
      "a && b || c": [
        { type: E_TOKEN_TYPE.FIELD, value: "a" },
        { type: E_TOKEN_TYPE.AND, value: "&&" },
        { type: E_TOKEN_TYPE.FIELD, value: "b" },
        { type: E_TOKEN_TYPE.OR, value: "||" },
        { type: E_TOKEN_TYPE.FIELD, value: "c" },
      ],
      "!a && (b || c)": [
        { type: E_TOKEN_TYPE.NOT, value: "!" },
        { type: E_TOKEN_TYPE.FIELD, value: "a" },
        { type: E_TOKEN_TYPE.AND, value: "&&" },
        { type: E_TOKEN_TYPE.LPAREN, value: "(" },
        { type: E_TOKEN_TYPE.FIELD, value: "b" },
        { type: E_TOKEN_TYPE.OR, value: "||" },
        { type: E_TOKEN_TYPE.FIELD, value: "c" },
        { type: E_TOKEN_TYPE.RPAREN, value: ")" },
      ],
      // Mixed arithmetic and comparison
      "a + b >= c * d": [
        { type: E_TOKEN_TYPE.FIELD, value: "a" },
        { type: E_TOKEN_TYPE.PLUS, value: "+" },
        { type: E_TOKEN_TYPE.FIELD, value: "b" },
        { type: E_TOKEN_TYPE.GREATER_EQUAL, value: ">=" },
        { type: E_TOKEN_TYPE.FIELD, value: "c" },
        { type: E_TOKEN_TYPE.MULTIPLY, value: "*" },
        { type: E_TOKEN_TYPE.FIELD, value: "d" },
      ],
      "(x - y) / 2 == z": [
        { type: E_TOKEN_TYPE.LPAREN, value: "(" },
        { type: E_TOKEN_TYPE.FIELD, value: "x" },
        { type: E_TOKEN_TYPE.MINUS, value: "-" },
        { type: E_TOKEN_TYPE.FIELD, value: "y" },
        { type: E_TOKEN_TYPE.RPAREN, value: ")" },
        { type: E_TOKEN_TYPE.DIVIDE, value: "/" },
        { type: E_TOKEN_TYPE.NUMBER, value: "2" },
        { type: E_TOKEN_TYPE.EQUALS, value: "==" },
        { type: E_TOKEN_TYPE.FIELD, value: "z" },
      ],
      // String comparisons with special characters
      'name == "John\'s Place"': [
        { type: E_TOKEN_TYPE.FIELD, value: "name" },
        { type: E_TOKEN_TYPE.EQUALS, value: "==" },
        { type: E_TOKEN_TYPE.STRING, value: "John's Place" },
      ],
      "description != 'Line 1\\nLine 2'": [
        { type: E_TOKEN_TYPE.FIELD, value: "description" },
        { type: E_TOKEN_TYPE.NOT_EQUALS, value: "!=" },
        { type: E_TOKEN_TYPE.STRING, value: "Line 1\nLine 2" },
      ],
      // Decimal numbers
      "price >= 19.99 && discount <= 0.5": [
        { type: E_TOKEN_TYPE.FIELD, value: "price" },
        { type: E_TOKEN_TYPE.GREATER_EQUAL, value: ">=" },
        { type: E_TOKEN_TYPE.NUMBER, value: "19.99" },
        { type: E_TOKEN_TYPE.AND, value: "&&" },
        { type: E_TOKEN_TYPE.FIELD, value: "discount" },
        { type: E_TOKEN_TYPE.LESS_EQUAL, value: "<=" },
        { type: E_TOKEN_TYPE.NUMBER, value: "0.5" },
      ],
      // Complex nested object paths
      "user.profile.settings.theme == 'dark'": [
        { type: E_TOKEN_TYPE.FIELD, value: "user" },
        { type: E_TOKEN_TYPE.DOT, value: "." },
        { type: E_TOKEN_TYPE.FIELD, value: "profile" },
        { type: E_TOKEN_TYPE.DOT, value: "." },
        { type: E_TOKEN_TYPE.FIELD, value: "settings" },
        { type: E_TOKEN_TYPE.DOT, value: "." },
        { type: E_TOKEN_TYPE.FIELD, value: "theme" },
        { type: E_TOKEN_TYPE.EQUALS, value: "==" },
        { type: E_TOKEN_TYPE.STRING, value: "dark" },
      ],
      // Multiple parentheses levels
      "((a + b) * (c - d)) / e": [
        { type: E_TOKEN_TYPE.LPAREN, value: "(" },
        { type: E_TOKEN_TYPE.LPAREN, value: "(" },
        { type: E_TOKEN_TYPE.FIELD, value: "a" },
        { type: E_TOKEN_TYPE.PLUS, value: "+" },
        { type: E_TOKEN_TYPE.FIELD, value: "b" },
        { type: E_TOKEN_TYPE.RPAREN, value: ")" },
        { type: E_TOKEN_TYPE.MULTIPLY, value: "*" },
        { type: E_TOKEN_TYPE.LPAREN, value: "(" },
        { type: E_TOKEN_TYPE.FIELD, value: "c" },
        { type: E_TOKEN_TYPE.MINUS, value: "-" },
        { type: E_TOKEN_TYPE.FIELD, value: "d" },
        { type: E_TOKEN_TYPE.RPAREN, value: ")" },
        { type: E_TOKEN_TYPE.RPAREN, value: ")" },
        { type: E_TOKEN_TYPE.DIVIDE, value: "/" },
        { type: E_TOKEN_TYPE.FIELD, value: "e" },
      ],
    },
    "Whitespace Handling": {
      // Various whitespace combinations
      "  a   +   b  ": [
        { type: E_TOKEN_TYPE.FIELD, value: "a" },
        { type: E_TOKEN_TYPE.PLUS, value: "+" },
        { type: E_TOKEN_TYPE.FIELD, value: "b" },
      ],
      "a\t+\tb": [
        { type: E_TOKEN_TYPE.FIELD, value: "a" },
        { type: E_TOKEN_TYPE.PLUS, value: "+" },
        { type: E_TOKEN_TYPE.FIELD, value: "b" },
      ],
      "a\n+\nb": [
        { type: E_TOKEN_TYPE.FIELD, value: "a" },
        { type: E_TOKEN_TYPE.PLUS, value: "+" },
        { type: E_TOKEN_TYPE.FIELD, value: "b" },
      ],
      "a\r\n+\r\nb": [
        { type: E_TOKEN_TYPE.FIELD, value: "a" },
        { type: E_TOKEN_TYPE.PLUS, value: "+" },
        { type: E_TOKEN_TYPE.FIELD, value: "b" },
      ],
      // Mixed whitespace in complex expressions
      " ( a  &&  b ) ||  ( c  !=  d ) ": [
        { type: E_TOKEN_TYPE.LPAREN, value: "(" },
        { type: E_TOKEN_TYPE.FIELD, value: "a" },
        { type: E_TOKEN_TYPE.AND, value: "&&" },
        { type: E_TOKEN_TYPE.FIELD, value: "b" },
        { type: E_TOKEN_TYPE.RPAREN, value: ")" },
        { type: E_TOKEN_TYPE.OR, value: "||" },
        { type: E_TOKEN_TYPE.LPAREN, value: "(" },
        { type: E_TOKEN_TYPE.FIELD, value: "c" },
        { type: E_TOKEN_TYPE.NOT_EQUALS, value: "!=" },
        { type: E_TOKEN_TYPE.FIELD, value: "d" },
        { type: E_TOKEN_TYPE.RPAREN, value: ")" },
      ],
    },
    "Edge Cases": {
      // Adjacent operators (should be parsed separately)
      "a+-b": [
        { type: E_TOKEN_TYPE.FIELD, value: "a" },
        { type: E_TOKEN_TYPE.PLUS, value: "+" },
        { type: E_TOKEN_TYPE.MINUS, value: "-" },
        { type: E_TOKEN_TYPE.FIELD, value: "b" },
      ],
      "a*/b": [
        { type: E_TOKEN_TYPE.FIELD, value: "a" },
        { type: E_TOKEN_TYPE.MULTIPLY, value: "*" },
        { type: E_TOKEN_TYPE.DIVIDE, value: "/" },
        { type: E_TOKEN_TYPE.FIELD, value: "b" },
      ],
      // Numbers with field names
      "field123 + 456field": [
        { type: E_TOKEN_TYPE.FIELD, value: "field123" },
        { type: E_TOKEN_TYPE.PLUS, value: "+" },
        { type: E_TOKEN_TYPE.NUMBER, value: "456" },
        { type: E_TOKEN_TYPE.FIELD, value: "field" },
      ],
      // Empty strings
      "name == ''": [
        { type: E_TOKEN_TYPE.FIELD, value: "name" },
        { type: E_TOKEN_TYPE.EQUALS, value: "==" },
        { type: E_TOKEN_TYPE.STRING, value: "" },
      ],
      'description != ""': [
        { type: E_TOKEN_TYPE.FIELD, value: "description" },
        { type: E_TOKEN_TYPE.NOT_EQUALS, value: "!=" },
        { type: E_TOKEN_TYPE.STRING, value: "" },
      ],
      // Single character field names
      "a==b&&c||d": [
        { type: E_TOKEN_TYPE.FIELD, value: "a" },
        { type: E_TOKEN_TYPE.EQUALS, value: "==" },
        { type: E_TOKEN_TYPE.FIELD, value: "b" },
        { type: E_TOKEN_TYPE.AND, value: "&&" },
        { type: E_TOKEN_TYPE.FIELD, value: "c" },
        { type: E_TOKEN_TYPE.OR, value: "||" },
        { type: E_TOKEN_TYPE.FIELD, value: "d" },
      ],
    },
  };

  map(entries(testsSets), ([setName, tests]) => {
    describe(setName, () => {
      map(entries(tests), ([input, expected]) => {
        test(input, () => {
          check(input, expected);
        });
      });
    });
  });
});
