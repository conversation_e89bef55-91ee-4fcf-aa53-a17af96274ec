{"name": "filter-dsl", "version": "1.0.0", "description": "", "repository": {"url": "https://github.com/NickSettler/filter-dsl", "type": "git"}, "main": "dist/index.js", "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Nick<PERSON>ettler"}, "scripts": {"run": "nodemon", "build": "tsc", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "test": "jest", "prepare": "husky"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.13", "@types/node": "^22.9.0", "eslint": "^9.13.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "globals": "^15.11.0", "husky": "^9.1.6", "jest": "^29.7.0", "lint-staged": "^15.2.10", "nodemon": "^3.1.7", "prettier": "^3.3.3", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5.6.3", "typescript-eslint": "^8.11.0"}, "lint-staged": {"*.ts": "eslint --cache --fix --max-warnings 0 src"}, "dependencies": {"lodash": "^4.17.21"}}