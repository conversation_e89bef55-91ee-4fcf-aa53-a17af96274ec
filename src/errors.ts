export const enum E_ERROR_CODES {
  /**
   * Lexer error code
   */
  LEXICAL_ERROR = 1,

  /**
   * Parser error code
   */
  SYNTAX_ERROR = 2,
}

/**
 * Lexer error class
 * @class
 * @classdesc Error class for lexer errors. It is thrown when lexer encounters an error (e.g. unexpected token).
 * @extends Error
 */
export class LexerError extends Error {
  /**
   * Error constructor
   * @constructor
   * @param {string} message Error message
   */
  constructor(message: string) {
    super(message);
    this.name = "LexicalError";
  }
}

/**
 * Parser error class
 * @class
 * @classdesc Error class for parser errors. It is thrown when parser encounters an error (e.g. unexpected token).
 * @extends Error
 */
export class ParserError extends Error {
  /**
   * Error constructor
   * @constructor
   * @param {string} message Error message
   */
  constructor(message: string) {
    super(message);
    this.name = "SyntaxError";
  }
}

export const error = (code: E_ERROR_CODES, message: string): never => {
  switch (code) {
    case E_ERROR_CODES.LEXICAL_ERROR:
      throw new LexerError(message);
    case E_ERROR_CODES.SYNTAX_ERROR:
      throw new SyntaxError(message);
    default:
      throw new Error(message);
  }
};
