export enum E_TOKEN_TYPE {
  // Literals
  STRING = "STRING",
  NUMBER = "NUMBER",
  BOOLEAN = "BOOLEAN",
  FIELD = "FIELD",

  // Operators
  PLUS = "PLUS",
  MINUS = "MINUS",
  MULTIPLY = "MULTIPLY",
  DIVIDE = "DIVIDE",

  // Comparison
  EQUALS = "EQUALS",
  NOT_EQUALS = "NOT_EQUALS",
  LESS_THAN = "LESS_THAN",
  GREATER_THAN = "GREATER_THAN",
  LESS_EQUAL = "LESS_EQUAL",
  GREATER_EQUAL = "GREATER_EQUAL",

  // Logical
  AND = "AND",
  OR = "OR",
  NOT = "NOT",

  // Brackets
  LPAREN = "LPAREN",
  RPAREN = "RPAREN",

  // Special
  DOT = "DOT", // For object paths like user.name
  EOF = "EOF",
}

export type TToken = {
  value: string;
  type: E_TOKEN_TYPE;
  position: number;
  length: number;
};
