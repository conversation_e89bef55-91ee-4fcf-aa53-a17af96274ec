import { E_TOKEN_TYPE, TToken } from "./types";
import { E_ERROR_CODES, error, LexerError } from "./errors";

enum E_LEXER_STATE {
  START = "START",
  STRING = "STRING",
  NUMBER = "NUMBER",
  FIELD = "FIELD",
  EQUALS = "EQUALS",
  NOT = "NOT",
  LESS = "LESS",
  GREATER = "GREATER",
  AND = "AND",
  OR = "OR",
}

export class QueryLexer {
  private position: number;
  private state: E_LEXER_STATE;
  private current: string;
  private tokenStart: number;

  constructor(private readonly input: string) {
    this.position = 0;
    this.state = E_LEXER_STATE.START;
    this.current = this.input[0] || "";
    this.tokenStart = 0;
  }

  private advance(): void {
    this.position++;
    this.current = this.input[this.position] || "";
  }

  private peek(offset = 1): string {
    return this.input[this.position + offset] || "";
  }

  private createToken(type: E_TOKEN_TYPE, value?: string): TToken {
    const tokenValue =
      value !== undefined
        ? value
        : this.input.slice(this.tokenStart, this.position);
    return {
      value: tokenValue,
      type,
      position: this.tokenStart,
      length: this.position - this.tokenStart,
    };
  }

  private skipWhitespace(): void {
    while (this.current && /\s/.test(this.current)) {
      this.advance();
    }
    this.tokenStart = this.position;
  }

  private isAlpha(char: string): boolean {
    return /[a-zA-Z_]/.test(char);
  }

  private isAlphaNum(char: string): boolean {
    return /[a-zA-Z0-9_]/.test(char);
  }

  private isDigit(char: string): boolean {
    return /[0-9]/.test(char);
  }

  /**
   * Main tokenization method - FSM implementation
   * "Simple code is debuggable code" - Linus
   */
  public nextToken(): TToken {
    while (this.current) {
      switch (this.state) {
        case E_LEXER_STATE.START:
          this.skipWhitespace();
          if (!this.current) break;

          this.tokenStart = this.position;

          // Single character tokens - no special cases needed
          switch (this.current) {
            case "+":
              this.advance();
              return this.createToken(E_TOKEN_TYPE.PLUS);
            case "-":
              this.advance();
              return this.createToken(E_TOKEN_TYPE.MINUS);
            case "*":
              this.advance();
              return this.createToken(E_TOKEN_TYPE.MULTIPLY);
            case "/":
              this.advance();
              return this.createToken(E_TOKEN_TYPE.DIVIDE);
            case "(":
              this.advance();
              return this.createToken(E_TOKEN_TYPE.LPAREN);
            case ")":
              this.advance();
              return this.createToken(E_TOKEN_TYPE.RPAREN);
            case ".":
              this.advance();
              return this.createToken(E_TOKEN_TYPE.DOT);
            case "\0":
              this.advance();
              return this.createToken(E_TOKEN_TYPE.EOF);
          }

          // Multi-character tokens - transition to appropriate state
          if (this.current === '"' || this.current === "'") {
            this.state = E_LEXER_STATE.STRING;
            this.advance(); // Skip opening quote
            continue;
          }

          if (this.isDigit(this.current)) {
            this.state = E_LEXER_STATE.NUMBER;
            continue;
          }

          if (this.isAlpha(this.current)) {
            this.state = E_LEXER_STATE.FIELD;
            continue;
          }

          // Two-character operators
          if (this.current === "=") {
            this.state = E_LEXER_STATE.EQUALS;
            continue;
          }

          if (this.current === "!") {
            this.state = E_LEXER_STATE.NOT;
            continue;
          }

          if (this.current === "<") {
            this.state = E_LEXER_STATE.LESS;
            continue;
          }

          if (this.current === ">") {
            this.state = E_LEXER_STATE.GREATER;
            continue;
          }

          if (this.current === "&") {
            this.state = E_LEXER_STATE.AND;
            continue;
          }

          if (this.current === "|") {
            this.state = E_LEXER_STATE.OR;
            continue;
          }

          // Unknown character
          this.advance();

          throw new LexerError(
            `Unsupported character ${this.current} in ${this.current} at ${this.tokenStart}:${this.position}`,
          );

        case E_LEXER_STATE.STRING:
          const quote = this.input[this.tokenStart];
          let stringValue = "";

          while (this.current && this.current !== quote) {
            if (this.current === "\\") {
              this.position++;
              this.current = this.input[this.position] || "";

              switch (this.current) {
                case "n":
                  stringValue += "\n";
                  break;
                case "t":
                  stringValue += "\t";
                  break;
                case "r":
                  stringValue += "\r";
                  break;
                case "\\":
                  stringValue += "\\";
                  break;
                case '"':
                  stringValue += '"';
                  break;
                case "'":
                  stringValue += "'";
                  break;
                default:
                  stringValue += this.current;
                  break;
              }
              this.advance();
            }
            stringValue += this.current;
            this.advance();
          }

          if (this.current === quote) {
            this.advance(); // Skip closing quote
          }

          this.state = E_LEXER_STATE.START;
          return this.createToken(E_TOKEN_TYPE.STRING, stringValue);

        case E_LEXER_STATE.NUMBER:
          let numberValue = "";
          let hasDot = false;

          while (
            this.current &&
            (this.isDigit(this.current) || (this.current === "." && !hasDot))
          ) {
            if (this.current === ".") hasDot = true;
            numberValue += this.current;
            this.advance();
          }

          this.state = E_LEXER_STATE.START;
          return this.createToken(E_TOKEN_TYPE.NUMBER, numberValue);

        case E_LEXER_STATE.FIELD:
          let fieldValue = "";

          while (this.current && this.isAlphaNum(this.current)) {
            fieldValue += this.current;
            this.advance();
          }

          this.state = E_LEXER_STATE.START;

          // Check for boolean literals and keywords
          switch (fieldValue.toLowerCase()) {
            case "true":
            case "false":
              return this.createToken(
                E_TOKEN_TYPE.BOOLEAN,
                fieldValue.toLowerCase(),
              );
            case "and":
              return this.createToken(E_TOKEN_TYPE.AND, "&&");
            case "or":
              return this.createToken(E_TOKEN_TYPE.OR, "||");
            case "not":
              return this.createToken(E_TOKEN_TYPE.NOT, "!");
            default:
              return this.createToken(E_TOKEN_TYPE.FIELD, fieldValue);
          }

        case E_LEXER_STATE.EQUALS:
          this.advance(); // Skip '='
          if (this.current === "=") {
            this.advance();
            this.state = E_LEXER_STATE.START;
            return this.createToken(E_TOKEN_TYPE.EQUALS, "==");
          }
          this.state = E_LEXER_STATE.START;

          throw new LexerError(
            `Unsupported character ${this.current} at ${this.tokenStart}:${this.position}`,
          );

        case E_LEXER_STATE.NOT:
          this.advance(); // Skip '!'
          if (this.current === "=") {
            this.advance();
            this.state = E_LEXER_STATE.START;
            return this.createToken(E_TOKEN_TYPE.NOT_EQUALS, "!=");
          }
          this.state = E_LEXER_STATE.START;
          return this.createToken(E_TOKEN_TYPE.NOT, "!");

        case E_LEXER_STATE.LESS:
          this.advance(); // Skip '<'
          if (this.current === "=") {
            this.advance();
            this.state = E_LEXER_STATE.START;
            return this.createToken(E_TOKEN_TYPE.LESS_EQUAL, "<=");
          }
          this.state = E_LEXER_STATE.START;
          return this.createToken(E_TOKEN_TYPE.LESS_THAN, "<");

        case E_LEXER_STATE.GREATER:
          this.advance(); // Skip '>'
          if (this.current === "=") {
            this.advance();
            this.state = E_LEXER_STATE.START;
            return this.createToken(E_TOKEN_TYPE.GREATER_EQUAL, ">=");
          }
          this.state = E_LEXER_STATE.START;
          return this.createToken(E_TOKEN_TYPE.GREATER_THAN, ">");

        case E_LEXER_STATE.AND:
          this.advance(); // Skip '&'
          if (this.current === "&") {
            this.advance();
            this.state = E_LEXER_STATE.START;
            return this.createToken(E_TOKEN_TYPE.AND, "&&");
          }
          this.state = E_LEXER_STATE.START;

          throw new LexerError(
            `Unsupported character ${this.current} at ${this.tokenStart}:${this.position}`,
          );

        case E_LEXER_STATE.OR:
          this.advance(); // Skip '|'
          if (this.current === "|") {
            this.advance();
            this.state = E_LEXER_STATE.START;
            return this.createToken(E_TOKEN_TYPE.OR, "||");
          }
          this.state = E_LEXER_STATE.START;

          throw new LexerError(
            `Unsupported character ${this.current} at ${this.tokenStart}:${this.position}`,
          );
      }
    }

    // End of input
    return this.createToken(E_TOKEN_TYPE.EOF, "");
  }

  /**
   * Tokenize entire input into array
   * Convenience method for testing and debugging
   */
  public tokenize(): Array<TToken> {
    const tokens: Array<TToken> = [];
    let token = this.nextToken();

    while (token.type !== E_TOKEN_TYPE.EOF) {
      tokens.push(token);
      token = this.nextToken();
    }

    tokens.push(token); // Add EOF token
    return tokens;
  }
}
